<template>
	<view class=" height-100vh flex flex-column page-wrap box-border">
		<view class="position-relative bgWrap zindex-5 flex-1 flex">
			<view class=" position-relative bg-content flex-1 width-100">
				<view class="position-absolute left-0 top-0 right-0 bottom-0">
					<image src="/static/images/<EMAIL>" mode="aspectFill" class="width-100 height-100"></image>
				</view>
			</view>
			<view class="position-absolute left-0 top-0 right-0 bottom-0 zindex-4  text-white flex flex-column">
				<view class="page-title-box flex-shrink  text-center flex align-center justify-center font-weight-500 font-m">
					我的名片
				</view>
				<view class="p-base flex-1  flex flex-column">
					<view class="flex justify-between flex-shrink pt-base">
						<view class="logo-box mr-base">
							<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
						</view>
						<view class="share-box flex align-center justify-center p-1 box-border">
							<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
						</view>
					</view>
					<view class="flex-1">
						
					</view>
					<view class="mb-auto flex-shrink flex align-center">
						<view class="more-box mr-4">
							<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
						</view>
						<view>
							<view class=" font-weight-bold font-m mb">陈功</view>
							<view class=" flex align-center">
								易慧科技有限公司<span class="mx-1">|</span>
								高级销售经理
							</view>
						</view>
					</view>
				</view>
			</view>
			
		</view>
		<view class="px-base pb-base pt-base width-100 box-border flex-shrink">
			<view class="scroll-row mb-base overflow-x-auto no-scrollbar  box-border">
				<view v-for="(item,index) in contactList" :key='index'  class="d-inline-block " :class="index===contactList.length-1 ? '' : 'mr-base'">
					<view class="rounded-8 p-base box-border border flex flex-column justify-evenly contact-item">
						<view class="flex align-center mb-1">
							<view class="icon-box mr-base">
								<image :src="item.icon" mode="aspectFit" class="width-100 height-100"></image>
							</view>
							<view class="text-theme-brand font-xs">{{item.title}}</view>
						</view>
						<view class="text-third width-100 text-ellipsis">{{item.desc}}</view>
					</view>
				</view>
			</view>
			<view class="text-third mb-base font-12">
				您好，我是易慧科技有限公司的高级销售经理陈工，很高兴认识您，希望我们能达成合作！
			</view>
			<view class="scroll-row mb-base overflow-x-auto no-scrollbar">
				<view v-for="(item,index) in preQuestionList" :key="index" class="rounded-80 py-1 px-3 text-second bg-info font-s mr-base mb scroll-row-item">
					{{item.title}}
				</view>
			</view>
			<view class=" bg-theme-brand text-white text-center normal-button-rounded flex align-center justify-center" @click='onOpenChat'>
				<span class="mx">AI</span>聊一聊
			</view>
		</view>
	</view>
</template>

<script setup>
	import {ref} from "vue"
	import PHONE_ICON from "/static/images/<EMAIL>"
	import WECHAT_ICON from "/static/images/<EMAIL>"
	import EMAIL_ICON from "/static/images/<EMAIL>"
	import ADDRESS_ICON from "/static/images/<EMAIL>"
	

	const CONTACT_LIST=[
		{title:"联系电话",desc:"125024369784",icon:PHONE_ICON},
		{title:"微信",desc:"11233ssssfffg",icon:WECHAT_ICON},
		{title:"邮箱",desc:"<EMAIL>",icon:EMAIL_ICON},
		{title:"公司地址",desc:"浙江省嘉兴市你那湖区，浙江大学嘉兴研究院111111111111装置帮",icon:ADDRESS_ICON},
	]
	const PREQUESTION_LIST=[
		{title:'了解产品',value:"0"},
		{title:'性价比最高的产品',value:"1"},
		{title:'试用场景有哪些?',value:"2"},
    ]
	// 变量
  const preQuestionList=ref(PREQUESTION_LIST)  // 预设的问题
  const contactList=ref(CONTACT_LIST) // 联系方式
	// 函数
	// 跳转到聊天
	function onOpenChat() {
		uni.navigateTo({
			url:"/pages/chat/chat"
		})
	}
</script>

<style lang="scss" scoped>
	.page-title-box{
		margin-top: 5rpx;
		padding-top:var(--status-bar-height);
		// height: 116rpx;
	}
	.bgWrap{
		border-bottom-left-radius: 16rpx;
		border-bottom-right-radius: 16rpx;
		overflow: hidden;
		.logo-box{
			width: 220rpx;
			height: 50rpx;
		}
		.share-box{
			width: 48rpx;
			height: 48rpx;
			border-radius: 100%;
			background-color: rgba(#000,0.3);
		}
		.more-box{
			width: 12rpx;
			height: 66rpx;
		}
	}
	.bg-content{
		// padding-bottom:146.4%;
		// height:0;
		
	}
	.contact-item{
		min-width:284rpx;
		max-width: 380rpx;
		height: 140rpx;
		.icon-box{
			width:40rpx;
			height:40rpx;
		}
	}
</style>