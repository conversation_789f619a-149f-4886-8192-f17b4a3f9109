<template>
	 <view class="wrap" :style="{ transform: `translateY(-${pageMove}px)` }">
	        <view class="main">Hello World</view>
	        <view  class="foot">
	           Input: <input placeholder='请输入内容' class="input" />
	        </view>
	     </view>
</template>
<script setup>
	import { ref, nextTick, watch,onMounted,onUnmounted, reactive } from 'vue';
	import {onLoad} from "@dcloudio/uni-app"
	const pageMove=ref(0)
	const keyboardListener=function (res){
		console.log("keyboardListener",res)
		nextTick(()=>{
			pageMove.value=res.height
		})
	}
	onLoad(()=>{
		uni.onKeyboardHeightChange(keyboardListener)
	})
	onUnmounted(()=>{
		uni.offKeyboardHeightChange(keyboardListener)
	})
</script>
<style lang="scss" scoped>
	.wrap{
	  height:100vh;
	display: flex;
	flex-direction: column;
	background-color: #f1f2f3;
	padding-bottom: 30px;
	}
	.main{
	  flex:1;
	  height: 0;
	}
	.foot{
	  display:flex;
	  align-items: center;
	  flex-shrink: 0;
	  padding:40px;
	  height:250px;
	  box-sizing: border-box;
	  border: 1px solid red;
	}
	.input{
	  background-color: #eee;
	  height:50px;
	  width: 100%;
	}
	.content{
		// height:calc(100vh - 200rpx);
		flex:1;
		height: 0;
	}
	.foot{
		height:100px;
		
	}
</style>